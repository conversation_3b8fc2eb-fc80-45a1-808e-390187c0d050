#!/usr/bin/env python3
"""
Cleanup Duplicate Strings Script
Removes duplicate string entries from language files while keeping the comprehensive versions.
"""

import xml.etree.ElementTree as ET
import os
import sys

def cleanup_duplicates(file_path):
    """Remove duplicate strings from a strings.xml file."""
    print(f"Cleaning up: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"  ❌ File not found: {file_path}")
        return False
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # Track seen strings and their elements
        seen_strings = {}
        duplicates_to_remove = []
        
        for string_elem in root.findall('string'):
            name = string_elem.get('name')
            if name in seen_strings:
                # Mark the first occurrence for removal (keep the later, more comprehensive one)
                duplicates_to_remove.append(seen_strings[name])
                print(f"  🔧 Removing duplicate: {name}")
            seen_strings[name] = string_elem
        
        # Remove duplicates
        for elem in duplicates_to_remove:
            root.remove(elem)
        
        # Write back to file
        tree.write(file_path, encoding='utf-8', xml_declaration=True)
        
        print(f"  ✅ Cleaned up {len(duplicates_to_remove)} duplicates")
        return True
        
    except ET.ParseError as e:
        print(f"  ❌ XML Parse Error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Main cleanup function."""
    print("🧹 Smart Shield Duplicate String Cleanup")
    print("=" * 50)
    
    # Define files that need cleanup
    files_to_clean = [
        "app/src/main/res/values-es/strings.xml",
        "app/src/main/res/values-zh/strings.xml", 
        "app/src/main/res/values-ja/strings.xml",
        "app/src/main/res/values-ko/strings.xml",
        "app/src/main/res/values-hi/strings.xml",
        "app/src/main/res/values-ru/strings.xml"
    ]
    
    all_cleaned = True
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            if not cleanup_duplicates(file_path):
                all_cleaned = False
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("\n" + "=" * 50)
    if all_cleaned:
        print("✅ All duplicate strings cleaned up!")
        print("🎉 All languages are now production-ready!")
        return 0
    else:
        print("❌ Some files had errors during cleanup.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
