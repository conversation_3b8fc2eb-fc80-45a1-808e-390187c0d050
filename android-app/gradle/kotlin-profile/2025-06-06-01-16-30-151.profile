BUILD_FAILED=false
BUILD_FINISH_TIME=1749165431717
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=42774
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=15
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=41566
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=4
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=3
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
BUILD_FAILED=false
BUILD_FINISH_TIME=1749166156342
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=135703
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=16
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=134749
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=2
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=2
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
BUILD_FAILED=false
BUILD_FINISH_TIME=1749166696395
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=43043
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=17
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=42544
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=13
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=2
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
BUILD_FAILED=false
BUILD_FINISH_TIME=1749169650242
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=42240
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=18
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=41417
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=1
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=1
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
BUILD_FAILED=false
BUILD_FINISH_TIME=1749169788856
BUILD_SRC_EXISTS=false
COMPILATION_STARTED=true
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
ENABLED_COMPILER_PLUGIN_PARSELIZE=true
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=7998
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=19
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=7535
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
JVM_DEFAULTS=disable
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_PROGRESSIVE_MODE=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=80
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=2
USE_CLASSPATH_SNAPSHOT=default-true
USE_FIR=false
BUILD FINISHED
BUILD_FAILED=false
BUILD_FINISH_TIME=1749169838949
BUILD_SRC_EXISTS=false
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=831
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=20
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=675
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=0
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=3
USE_CLASSPATH_SNAPSHOT=default-true
BUILD FINISHED
BUILD_FAILED=true
BUILD_FINISH_TIME=1749171022132
BUILD_SRC_EXISTS=false
CONFIGURATION_API_COUNT=1
CONFIGURATION_IMPLEMENTATION_COUNT=1
CPU_NUMBER_OF_CORES=8
EXECUTED_FROM_IDEA=true
GRADLE_BUILD_DURATION=2244
GRADLE_BUILD_NUMBER_IN_CURRENT_DAEMON=21
GRADLE_DAEMON_HEAP_SIZE=2000000000
GRADLE_EXECUTION_DURATION=1511
GRADLE_NUMBER_OF_TASKS=200
GRADLE_NUMBER_OF_UNCONFIGURED_TASKS=200
GRADLE_VERSION=8.11.1
KOTLIN_COMPILER_VERSION=1.9.22
KOTLIN_KTS_USED=false
KOTLIN_STDLIB_VERSION=1.9.22
NUMBER_OF_SUBPROJECTS=1
OS_TYPE=Mac OS X
PROJECT_PATH=/Volumes/wadie/Smart Shield/android-app
STATISTICS_COLLECT_METRICS_OVERHEAD=16
STATISTICS_VISIT_ALL_PROJECTS_OVERHEAD=3
USE_CLASSPATH_SNAPSHOT=default-true
BUILD FINISHED
