#!/usr/bin/env python3
"""
String Resource Validation Script
Validates that all string resources are properly defined and no duplicates exist.
"""

import xml.etree.ElementTree as ET
import os
import sys

def validate_strings_file(file_path):
    """Validate a strings.xml file for duplicates and syntax errors."""
    print(f"Validating: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"  ❌ File not found: {file_path}")
        return False
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # Check for duplicate string names
        string_names = []
        duplicates = []
        
        for string_elem in root.findall('string'):
            name = string_elem.get('name')
            if name in string_names:
                duplicates.append(name)
            else:
                string_names.append(name)
        
        if duplicates:
            print(f"  ❌ Found duplicate strings: {duplicates}")
            return False
        else:
            print(f"  ✅ No duplicates found. Total strings: {len(string_names)}")
            return True
            
    except ET.ParseError as e:
        print(f"  ❌ XML Parse Error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Main validation function."""
    print("🔍 Smart Shield String Resource Validation")
    print("=" * 50)
    
    # Define string resource files to validate
    string_files = [
        "app/src/main/res/values/strings.xml",
        "app/src/main/res/values-pt/strings.xml", 
        "app/src/main/res/values-fr/strings.xml",
        "app/src/main/res/values-de/strings.xml",
        "app/src/main/res/values-ar/strings.xml",
        "app/src/main/res/values-es/strings.xml",
        "app/src/main/res/values-zh/strings.xml",
        "app/src/main/res/values-ja/strings.xml",
        "app/src/main/res/values-ko/strings.xml",
        "app/src/main/res/values-hi/strings.xml",
        "app/src/main/res/values-ru/strings.xml"
    ]
    
    all_valid = True
    
    for file_path in string_files:
        if os.path.exists(file_path):
            if not validate_strings_file(file_path):
                all_valid = False
        else:
            print(f"⚠️  Optional file not found: {file_path}")
    
    print("\n" + "=" * 50)
    if all_valid:
        print("✅ All string resource files are valid!")
        print("🎉 Language switching should work correctly now.")
        return 0
    else:
        print("❌ Some string resource files have errors.")
        print("🔧 Please fix the duplicate strings before building.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
