package com.smartshield.securityapp.ui

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import timber.log.Timber

class FeatureShowcaseActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            SmartShieldTheme {
                FeatureShowcaseScreen(
                    onGetStarted = {
                        navigateToOnboarding()
                    },
                    onFeatureClick = { feature ->
                        showLockedFeatureDialog(feature)
                    }
                )
            }
        }
    }

    private fun navigateToOnboarding() {
        val intent = Intent(this, OnboardingActivity::class.java)
        startActivity(intent)
        finish()
    }

    private fun showLockedFeatureDialog(feature: String) {
        Timber.d("Feature clicked: $feature (locked)")
        // Show dialog that feature is locked until setup is complete
    }
}

data class FeatureItem(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val color: Color,
    val isLocked: Boolean = true
)

@Composable
fun FeatureShowcaseScreen(
    onGetStarted: () -> Unit,
    onFeatureClick: (String) -> Unit
) {
    var showLockedDialog by remember { mutableStateOf(false) }
    var selectedFeature by remember { mutableStateOf("") }

    val features = listOf(
        FeatureItem("Dashboard", "Real-time device monitoring", Icons.Default.Dashboard, Color(0xFF1E88E5)),
        FeatureItem("Live Tracking", "GPS location tracking", Icons.Default.LocationOn, Color(0xFF43A047)),
        FeatureItem("Media Gallery", "Photos, videos & audio", Icons.Default.PhotoLibrary, Color(0xFFE91E63)),
        FeatureItem("Device Control", "Remote device management", Icons.Default.Devices, Color(0xFF9C27B0)),
        FeatureItem("SOS Alerts", "Emergency notifications", Icons.Default.Emergency, Color(0xFFF44336)),
        FeatureItem("Parental Controls", "Child safety features", Icons.Default.FamilyRestroom, Color(0xFFFF5722)),
        FeatureItem("Trackers", "Pets, cars & AirTags", Icons.Default.Pets, Color(0xFF795548)),
        FeatureItem("Remote Shield", "Screen sharing & control", Icons.Default.ScreenShare, Color(0xFF2E7D32)),
        FeatureItem("AI Protect", "Smart security agent", Icons.Default.Psychology, Color(0xFF6366F1)),
        FeatureItem("Analytics", "Usage statistics", Icons.Default.Analytics, Color(0xFFFF9800)),
        FeatureItem("Settings", "App configuration", Icons.Default.Settings, Color(0xFF607D8B)),
        FeatureItem("Profile", "User management", Icons.Default.Person, Color(0xFF3F51B5))
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1E88E5),
                        Color(0xFF1565C0),
                        Color(0xFF0D47A1)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp)
        ) {
            Spacer(modifier = Modifier.height(40.dp))

            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Card(
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(16.dp)),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White.copy(alpha = 0.1f)
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "🛡️",
                            fontSize = 30.sp
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                Column {
                    Text(
                        text = "Smart Shield",
                        fontSize = 28.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Text(
                        text = "Professional Security System",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Features Grid
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.White.copy(alpha = 0.95f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Text(
                        text = "Powerful Features",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1E88E5),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Text(
                        text = "Unlock all features with Smart Shield Pro",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(20.dp))

                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                        modifier = Modifier.weight(1f)
                    ) {
                        items(features) { feature ->
                            FeatureCard(
                                feature = feature,
                                onClick = {
                                    selectedFeature = feature.title
                                    showLockedDialog = true
                                    onFeatureClick(feature.title)
                                }
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Get Started Button
            Button(
                onClick = onGetStarted,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                    contentColor = Color(0xFF1E88E5)
                ),
                shape = RoundedCornerShape(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Rocket,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Get Started",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Secure • Reliable • Professional",
                color = Color.White.copy(alpha = 0.7f),
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }

    // Locked Feature Dialog
    if (showLockedDialog) {
        AlertDialog(
            onDismissRequest = { showLockedDialog = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = null,
                        tint = Color(0xFFFF9800),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Feature Locked")
                }
            },
            text = {
                Text(
                    text = "The '$selectedFeature' feature is currently locked. Complete the setup process to unlock all Smart Shield features.",
                    fontSize = 16.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showLockedDialog = false
                        onGetStarted()
                    }
                ) {
                    Text("Get Started")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showLockedDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
fun FeatureCard(
    feature: FeatureItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = feature.color.copy(alpha = 0.1f)
        ),
        border = androidx.compose.foundation.BorderStroke(
            1.dp,
            feature.color.copy(alpha = 0.3f)
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(12.dp)
            ) {
                Box {
                    Icon(
                        imageVector = feature.icon,
                        contentDescription = null,
                        tint = feature.color,
                        modifier = Modifier.size(32.dp)
                    )

                    if (feature.isLocked) {
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Locked",
                            tint = Color(0xFFFF9800),
                            modifier = Modifier
                                .size(16.dp)
                                .offset(x = 20.dp, y = (-8).dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = feature.title,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = feature.color,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )

                Text(
                    text = feature.description,
                    fontSize = 10.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    maxLines = 2
                )
            }
        }
    }
}
