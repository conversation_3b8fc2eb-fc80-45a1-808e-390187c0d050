package com.smartshield.securityapp.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.smartshield.securityapp.managers.SecurityServiceManager
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import timber.log.Timber

class AdvancedSettingsActivity : BaseActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SmartShieldTheme {
                AdvancedSettingsScreen(
                    onBack = { finish() }
                )
            }
        }
    }
}


@Composable
fun AdvancedSettingsScreen(
    onBack: () -> Unit
) {
    val context = LocalContext.current
    val securityServiceManager = remember { SecurityServiceManager(context) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Settings",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF1E88E5),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Security Settings
            item {
                SettingsSection(
                    title = "Security Settings",
                    icon = Icons.Default.Security
                ) {
                    SecuritySettings(securityServiceManager)
                }
            }
            
            // Location Settings
            item {
                SettingsSection(
                    title = "Location Tracking",
                    icon = Icons.Default.LocationOn
                ) {
                    LocationSettings(securityServiceManager)
                }
            }
            
            // Media Settings
            item {
                SettingsSection(
                    title = "Media Capture",
                    icon = Icons.Default.Camera
                ) {
                    MediaSettings(securityServiceManager)
                }
            }
            
            // SOS Settings
            item {
                SettingsSection(
                    title = "Emergency SOS",
                    icon = Icons.Default.Emergency
                ) {
                    SOSSettings(securityServiceManager)
                }
            }
            
            // Privacy Settings
            item {
                SettingsSection(
                    title = "Privacy & Data",
                    icon = Icons.Default.PrivacyTip
                ) {
                    PrivacySettings()
                }
            }
            
            // Device Settings
            item {
                SettingsSection(
                    title = "Device Management",
                    icon = Icons.Default.PhoneAndroid
                ) {
                    DeviceSettings()
                }
            }
            
            // App Settings
            item {
                SettingsSection(
                    title = "App Settings",
                    icon = Icons.Default.Settings
                ) {
                    AppSettings()
                }
            }
        }
    }
}

@Composable
fun SettingsSection(
    title: String,
    icon: ImageVector,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color(0xFF1E88E5),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF1E88E5)
                )
            }
            
            content()
        }
    }
}

@Composable
fun SecuritySettings(securityServiceManager: SecurityServiceManager) {
    var locationTrackingEnabled by remember { 
        mutableStateOf(securityServiceManager.isLocationTrackingEnabled()) 
    }
    var sosMonitoringEnabled by remember { 
        mutableStateOf(securityServiceManager.isSOSMonitoringEnabled()) 
    }
    var autoCaptureEnabled by remember { 
        mutableStateOf(securityServiceManager.isAutoCaptureEnabled()) 
    }
    var stealthMode by remember { 
        mutableStateOf(securityServiceManager.isStealthMode()) 
    }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsToggle(
            title = "Location Tracking",
            description = "Track device location in background",
            checked = locationTrackingEnabled,
            onCheckedChange = { enabled ->
                locationTrackingEnabled = enabled
                securityServiceManager.setLocationTrackingEnabled(enabled)
            }
        )
        
        SettingsToggle(
            title = "SOS Monitoring",
            description = "Monitor for emergency situations",
            checked = sosMonitoringEnabled,
            onCheckedChange = { enabled ->
                sosMonitoringEnabled = enabled
                securityServiceManager.setSOSMonitoringEnabled(enabled)
            }
        )
        
        SettingsToggle(
            title = "Auto Capture",
            description = "Automatically capture evidence",
            checked = autoCaptureEnabled,
            onCheckedChange = { enabled ->
                autoCaptureEnabled = enabled
                securityServiceManager.setAutoCaptureEnabled(enabled)
            }
        )
        
        SettingsToggle(
            title = "Stealth Mode",
            description = "Hide capture activities from user",
            checked = stealthMode,
            onCheckedChange = { enabled ->
                stealthMode = enabled
                securityServiceManager.setStealthMode(enabled)
            }
        )
    }
}

@Composable
fun LocationSettings(securityServiceManager: SecurityServiceManager) {
    var trackingFrequency by remember { 
        mutableStateOf(securityServiceManager.getTrackingFrequency()) 
    }
    var highAccuracyMode by remember { 
        mutableStateOf(securityServiceManager.isHighAccuracyMode()) 
    }
    var batterySaverMode by remember { 
        mutableStateOf(securityServiceManager.isBatterySaverMode()) 
    }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsSlider(
            title = "Tracking Frequency",
            description = "Update interval in minutes",
            value = trackingFrequency.toFloat(),
            valueRange = 1f..60f,
            steps = 59,
            onValueChange = { value ->
                trackingFrequency = value.toInt()
                securityServiceManager.setTrackingFrequency(trackingFrequency)
            },
            valueFormatter = { "${it.toInt()} min" }
        )
        
        SettingsToggle(
            title = "High Accuracy Mode",
            description = "Use GPS for precise location",
            checked = highAccuracyMode,
            onCheckedChange = { enabled ->
                highAccuracyMode = enabled
                securityServiceManager.setHighAccuracyMode(enabled)
            }
        )
        
        SettingsToggle(
            title = "Battery Saver Mode",
            description = "Reduce location updates to save battery",
            checked = batterySaverMode,
            onCheckedChange = { enabled ->
                batterySaverMode = enabled
                securityServiceManager.setBatterySaverMode(enabled)
            }
        )
    }
}

@Composable
fun MediaSettings(securityServiceManager: SecurityServiceManager) {
    var autoUpload by remember { mutableStateOf(true) }
    var imageQuality by remember { mutableStateOf(80f) }
    var videoQuality by remember { mutableStateOf(720f) }
    var maxStorageSize by remember { mutableStateOf(1000f) }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsToggle(
            title = "Auto Upload",
            description = "Automatically upload captured media",
            checked = autoUpload,
            onCheckedChange = { autoUpload = it }
        )
        
        SettingsSlider(
            title = "Image Quality",
            description = "JPEG compression quality",
            value = imageQuality,
            valueRange = 50f..100f,
            steps = 50,
            onValueChange = { imageQuality = it },
            valueFormatter = { "${it.toInt()}%" }
        )
        
        SettingsSlider(
            title = "Video Quality",
            description = "Video resolution",
            value = videoQuality,
            valueRange = 480f..1080f,
            steps = 2,
            onValueChange = { videoQuality = it },
            valueFormatter = { "${it.toInt()}p" }
        )
        
        SettingsSlider(
            title = "Max Storage",
            description = "Maximum local storage in MB",
            value = maxStorageSize,
            valueRange = 100f..5000f,
            steps = 49,
            onValueChange = { maxStorageSize = it },
            valueFormatter = { "${it.toInt()} MB" }
        )
    }
}

@Composable
fun SOSSettings(securityServiceManager: SecurityServiceManager) {
    var shakeDetection by remember { mutableStateOf(true) }
    var powerButtonSOS by remember { mutableStateOf(true) }
    var autoCapture by remember { mutableStateOf(true) }
    var emergencyContacts by remember { mutableStateOf(true) }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsToggle(
            title = "Shake Detection",
            description = "Trigger SOS by shaking device",
            checked = shakeDetection,
            onCheckedChange = { shakeDetection = it }
        )
        
        SettingsToggle(
            title = "Power Button SOS",
            description = "Triple press power button for SOS",
            checked = powerButtonSOS,
            onCheckedChange = { powerButtonSOS = it }
        )
        
        SettingsToggle(
            title = "Auto Evidence Capture",
            description = "Capture photos/audio during SOS",
            checked = autoCapture,
            onCheckedChange = { autoCapture = it }
        )
        
        SettingsToggle(
            title = "Emergency Contacts",
            description = "Send alerts to emergency contacts",
            checked = emergencyContacts,
            onCheckedChange = { emergencyContacts = it }
        )
    }
}

@Composable
fun PrivacySettings() {
    var dataEncryption by remember { mutableStateOf(true) }
    var anonymousMode by remember { mutableStateOf(false) }
    var dataRetention by remember { mutableStateOf(30f) }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsToggle(
            title = "Data Encryption",
            description = "Encrypt all stored data",
            checked = dataEncryption,
            onCheckedChange = { dataEncryption = it }
        )
        
        SettingsToggle(
            title = "Anonymous Mode",
            description = "Hide device identifiers",
            checked = anonymousMode,
            onCheckedChange = { anonymousMode = it }
        )
        
        SettingsSlider(
            title = "Data Retention",
            description = "Days to keep data locally",
            value = dataRetention,
            valueRange = 1f..90f,
            steps = 89,
            onValueChange = { dataRetention = it },
            valueFormatter = { "${it.toInt()} days" }
        )
    }
}

@Composable
fun DeviceSettings() {
    var deviceAdmin by remember { mutableStateOf(true) }
    var autoStart by remember { mutableStateOf(true) }
    var hiddenMode by remember { mutableStateOf(false) }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsToggle(
            title = "Device Administrator",
            description = "Enable device admin privileges",
            checked = deviceAdmin,
            onCheckedChange = { deviceAdmin = it }
        )
        
        SettingsToggle(
            title = "Auto Start",
            description = "Start services on device boot",
            checked = autoStart,
            onCheckedChange = { autoStart = it }
        )
        
        SettingsToggle(
            title = "Hidden Mode",
            description = "Hide app from launcher",
            checked = hiddenMode,
            onCheckedChange = { hiddenMode = it }
        )
    }
}

@Composable
fun AppSettings() {
    var notifications by remember { mutableStateOf(true) }
    var darkMode by remember { mutableStateOf(false) }
    var language by remember { mutableStateOf("English") }
    
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        SettingsToggle(
            title = "Notifications",
            description = "Show app notifications",
            checked = notifications,
            onCheckedChange = { notifications = it }
        )
        
        SettingsToggle(
            title = "Dark Mode",
            description = "Use dark theme",
            checked = darkMode,
            onCheckedChange = { darkMode = it }
        )
        
        SettingsItem(
            title = "Language",
            description = language,
            onClick = {
                // TODO: Open language selector
                Timber.d("Language settings clicked")
            }
        )
    }
}

@Composable
fun SettingsToggle(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = description,
                fontSize = 14.sp,
                color = Color.Gray
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color(0xFF1E88E5),
                checkedTrackColor = Color(0xFF1E88E5).copy(alpha = 0.5f)
            )
        )
    }
}

@Composable
fun SettingsSlider(
    title: String,
    description: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    steps: Int,
    onValueChange: (Float) -> Unit,
    valueFormatter: (Float) -> String
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
            
            Text(
                text = valueFormatter(value),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1E88E5)
            )
        }
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            steps = steps,
            colors = SliderDefaults.colors(
                thumbColor = Color(0xFF1E88E5),
                activeTrackColor = Color(0xFF1E88E5)
            )
        )
    }
}

@Composable
fun SettingsItem(
    title: String,
    description: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = Color.Gray
                )
            }
            
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = Color.Gray
            )
        }
    }
}
