package com.smartshield.securityapp.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.MapView
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.gms.maps.model.PolylineOptions
import com.smartshield.securityapp.network.NetworkModule
import com.smartshield.securityapp.network.models.LocationData
import com.smartshield.securityapp.repository.LocationRepository
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import com.smartshield.securityapp.utils.TokenManager
import com.smartshield.securityapp.viewmodels.LocationViewModel
import com.smartshield.securityapp.viewmodels.LocationViewModelFactory
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

class LocationHistoryActivity : BaseActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SmartShieldTheme {
                LocationHistoryScreen(
                    onBack = { finish() }
                )
            }
        }
    }
}


@Composable
fun LocationHistoryScreen(
    onBack: () -> Unit
) {
    val context = LocalContext.current
    
    // Initialize ViewModel
    val tokenManager = remember { TokenManager(context) }
    val apiService = remember { NetworkModule.provideApiService(context) }
    val locationRepository = remember { LocationRepository(apiService, tokenManager) }
    val locationViewModel: LocationViewModel = viewModel(
        factory = LocationViewModelFactory(locationRepository)
    )
    
    var showMap by remember { mutableStateOf(true) }
    var selectedTimeRange by remember { mutableStateOf("today") }
    
    val locationHistory by locationViewModel.locationHistory.collectAsState()
    val isLoading by locationViewModel.isLoading.collectAsState()
    
    // Load location history on screen load
    LaunchedEffect(selectedTimeRange) {
        locationViewModel.loadLocationHistory(selectedTimeRange)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Location History",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { showMap = !showMap }
                    ) {
                        Icon(
                            imageVector = if (showMap) Icons.Default.List else Icons.Default.Map,
                            contentDescription = if (showMap) "Show List" else "Show Map"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF1E88E5),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Time Range Selector
            TimeRangeSelector(
                selectedRange = selectedTimeRange,
                onRangeSelected = { range ->
                    selectedTimeRange = range
                }
            )
            
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF1E88E5)
                    )
                }
            } else {
                if (showMap) {
                    // Map View
                    LocationMapView(
                        locations = locationHistory,
                        modifier = Modifier.weight(1f)
                    )
                } else {
                    // List View
                    LocationListView(
                        locations = locationHistory,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
fun TimeRangeSelector(
    selectedRange: String,
    onRangeSelected: (String) -> Unit
) {
    val timeRanges = listOf(
        "today" to "Today",
        "yesterday" to "Yesterday",
        "week" to "This Week",
        "month" to "This Month",
        "all" to "All Time"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Time Range",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1E88E5)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                timeRanges.forEach { (key, label) ->
                    FilterChip(
                        onClick = { onRangeSelected(key) },
                        label = { Text(label, fontSize = 12.sp) },
                        selected = selectedRange == key,
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = Color(0xFF1E88E5),
                            selectedLabelColor = Color.White
                        )
                    )
                }
            }
        }
    }
}

@Composable
fun LocationMapView(
    locations: List<LocationData>,
    modifier: Modifier = Modifier
) {
    if (locations.isEmpty()) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOff,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = Color.Gray
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No location data available",
                    fontSize = 16.sp,
                    color = Color.Gray
                )
            }
        }
        return
    }
    
    AndroidView(
        factory = { context ->
            MapView(context).apply {
                onCreate(null)
                onResume()
                getMapAsync { googleMap ->
                    setupMap(googleMap, locations)
                }
            }
        },
        modifier = modifier
    )
}

private fun setupMap(googleMap: GoogleMap, locations: List<LocationData>) {
    if (locations.isEmpty()) return
    
    // Add markers for each location
    locations.forEach { location ->
        val position = LatLng(location.latitude, location.longitude)
        googleMap.addMarker(
            MarkerOptions()
                .position(position)
                .title("Location")
                .snippet(formatTimestamp(location.timestamp))
        )
    }
    
    // Add polyline to show path
    if (locations.size > 1) {
        val polylineOptions = PolylineOptions()
            .color(android.graphics.Color.BLUE)
            .width(5f)
        
        locations.forEach { location ->
            polylineOptions.add(LatLng(location.latitude, location.longitude))
        }
        
        googleMap.addPolyline(polylineOptions)
    }
    
    // Move camera to show all locations
    val latLngBounds = com.google.android.gms.maps.model.LatLngBounds.Builder()
    locations.forEach { location ->
        latLngBounds.include(LatLng(location.latitude, location.longitude))
    }
    
    try {
        googleMap.moveCamera(
            CameraUpdateFactory.newLatLngBounds(latLngBounds.build(), 100)
        )
    } catch (e: Exception) {
        // Fallback to first location
        val firstLocation = locations.first()
        googleMap.moveCamera(
            CameraUpdateFactory.newLatLngZoom(
                LatLng(firstLocation.latitude, firstLocation.longitude),
                15f
            )
        )
    }
}

@Composable
fun LocationListView(
    locations: List<LocationData>,
    modifier: Modifier = Modifier
) {
    if (locations.isEmpty()) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOff,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = Color.Gray
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No location data available",
                    fontSize = 16.sp,
                    color = Color.Gray
                )
            }
        }
        return
    }
    
    LazyColumn(
        modifier = modifier.padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(locations) { location ->
            LocationItem(location = location)
        }
    }
}

@Composable
fun LocationItem(location: LocationData) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.LocationOn,
                contentDescription = null,
                tint = Color(0xFF1E88E5),
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = formatCoordinates(location.latitude, location.longitude),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF1E88E5)
                )
                
                Text(
                    text = formatTimestamp(location.timestamp),
                    fontSize = 14.sp,
                    color = Color.Gray
                )
                
                if (location.address != null) {
                    Text(
                        text = location.address,
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (location.accuracy != null) {
                        Text(
                            text = "±${location.accuracy.toInt()}m",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                    
                    if (location.batteryLevel != null) {
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            imageVector = Icons.Default.Battery6Bar,
                            contentDescription = null,
                            modifier = Modifier.size(12.dp),
                            tint = Color.Gray
                        )
                        Text(
                            text = "${location.batteryLevel}%",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                if (location.speed != null && location.speed > 0) {
                    Text(
                        text = "${location.speed.toInt()} km/h",
                        fontSize = 12.sp,
                        color = Color(0xFF4CAF50),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                if (location.networkType != null) {
                    Text(
                        text = location.networkType,
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

private fun formatCoordinates(lat: Double, lng: Double): String {
    return "${String.format("%.6f", lat)}, ${String.format("%.6f", lng)}"
}

private fun formatTimestamp(timestamp: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        val outputFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        val date = inputFormat.parse(timestamp)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        timestamp
    }
}
