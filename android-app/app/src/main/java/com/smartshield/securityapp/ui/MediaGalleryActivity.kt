package com.smartshield.securityapp.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.smartshield.securityapp.network.NetworkModule
import com.smartshield.securityapp.network.models.MediaData
import com.smartshield.securityapp.repository.MediaRepository
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import com.smartshield.securityapp.utils.TokenManager
import com.smartshield.securityapp.viewmodels.MediaViewModel
import com.smartshield.securityapp.viewmodels.MediaViewModelFactory
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

class MediaGalleryActivity : BaseActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SmartShieldTheme {
                MediaGalleryScreen(
                    onBack = { finish() }
                )
            }
        }
    }
}


@Composable
fun MediaGalleryScreen(
    onBack: () -> Unit
) {
    val context = LocalContext.current
    
    // Initialize ViewModel
    val tokenManager = remember { TokenManager(context) }
    val apiService = remember { NetworkModule.provideApiService(context) }
    val mediaRepository = remember { MediaRepository(apiService, tokenManager) }
    val mediaViewModel: MediaViewModel = viewModel(
        factory = MediaViewModelFactory(mediaRepository)
    )
    
    var selectedMediaType by remember { mutableStateOf("all") }
    var showFilterDialog by remember { mutableStateOf(false) }
    
    val mediaList by mediaViewModel.mediaList.collectAsState()
    val isLoading by mediaViewModel.isLoading.collectAsState()
    val error by mediaViewModel.error.collectAsState()
    
    // Load media on screen load
    LaunchedEffect(selectedMediaType) {
        mediaViewModel.loadMedia(selectedMediaType)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Media Gallery",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { showFilterDialog = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.FilterList,
                            contentDescription = "Filter"
                        )
                    }
                    
                    IconButton(
                        onClick = { mediaViewModel.refreshMedia() }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Refresh"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF1E88E5),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White,
                    actionIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Media Type Selector
            MediaTypeSelector(
                selectedType = selectedMediaType,
                onTypeSelected = { type ->
                    selectedMediaType = type
                }
            )
            
            // Error handling
            error?.let { errorMessage ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFFFEBEE)
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            tint = Color(0xFFD32F2F)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = errorMessage,
                            color = Color(0xFFD32F2F),
                            fontSize = 14.sp
                        )
                    }
                }
            }
            
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF1E88E5)
                    )
                }
            } else {
                // Media Grid
                MediaGrid(
                    mediaList = mediaList,
                    onMediaClick = { media ->
                        // TODO: Open media viewer
                        Timber.d("Media clicked: ${media.fileName}")
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
    
    // Filter Dialog
    if (showFilterDialog) {
        MediaFilterDialog(
            onDismiss = { showFilterDialog = false },
            onApplyFilter = { filterType ->
                selectedMediaType = filterType
                showFilterDialog = false
            }
        )
    }
}

@Composable
fun MediaTypeSelector(
    selectedType: String,
    onTypeSelected: (String) -> Unit
) {
    val mediaTypes = listOf(
        "all" to "All Media",
        "image" to "Photos",
        "video" to "Videos",
        "audio" to "Audio"
    )
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Media Type",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF1E88E5)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                mediaTypes.forEach { (key, label) ->
                    FilterChip(
                        onClick = { onTypeSelected(key) },
                        label = { Text(label, fontSize = 12.sp) },
                        selected = selectedType == key,
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = Color(0xFF1E88E5),
                            selectedLabelColor = Color.White
                        )
                    )
                }
            }
        }
    }
}

@Composable
fun MediaGrid(
    mediaList: List<MediaData>,
    onMediaClick: (MediaData) -> Unit,
    modifier: Modifier = Modifier
) {
    if (mediaList.isEmpty()) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.PhotoLibrary,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = Color.Gray
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No media files available",
                    fontSize = 16.sp,
                    color = Color.Gray
                )
            }
        }
        return
    }
    
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = modifier.padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(mediaList) { media ->
            MediaItem(
                media = media,
                onClick = { onMediaClick(media) }
            )
        }
    }
}

@Composable
fun MediaItem(
    media: MediaData,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            when (media.type) {
                "image" -> {
                    AsyncImage(
                        model = media.thumbnailUrl ?: media.fileUrl,
                        contentDescription = media.fileName,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                "video" -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Black),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Video",
                            tint = Color.White,
                            modifier = Modifier.size(48.dp)
                        )
                    }
                }
                "audio" -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color(0xFF2196F3)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.AudioFile,
                            contentDescription = "Audio",
                            tint = Color.White,
                            modifier = Modifier.size(48.dp)
                        )
                    }
                }
            }
            
            // Media info overlay
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        Color.Black.copy(alpha = 0.7f)
                    )
                    .align(Alignment.BottomCenter)
                    .padding(8.dp)
            ) {
                Column {
                    Text(
                        text = media.fileName,
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = formatTimestamp(media.capturedAt),
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 10.sp
                        )
                        
                        if (media.duration != null) {
                            Text(
                                text = formatDuration(media.duration),
                                color = Color.White.copy(alpha = 0.8f),
                                fontSize = 10.sp
                            )
                        }
                    }
                }
            }
            
            // Media type icon
            Card(
                modifier = Modifier
                    .padding(8.dp)
                    .align(Alignment.TopEnd),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                )
            ) {
                Icon(
                    imageVector = when (media.type) {
                        "image" -> Icons.Default.Photo
                        "video" -> Icons.Default.Videocam
                        "audio" -> Icons.Default.Mic
                        else -> Icons.Default.InsertDriveFile
                    },
                    contentDescription = media.type,
                    tint = Color.White,
                    modifier = Modifier
                        .padding(4.dp)
                        .size(16.dp)
                )
            }
        }
    }
}

@Composable
fun MediaFilterDialog(
    onDismiss: () -> Unit,
    onApplyFilter: (String) -> Unit
) {
    var selectedFilter by remember { mutableStateOf("all") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Filter Media")
        },
        text = {
            Column {
                Text("Select media type to display:")
                
                Spacer(modifier = Modifier.height(16.dp))
                
                val filterOptions = listOf(
                    "all" to "All Media",
                    "image" to "Photos Only",
                    "video" to "Videos Only",
                    "audio" to "Audio Only"
                )
                
                filterOptions.forEach { (key, label) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { selectedFilter = key }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedFilter == key,
                            onClick = { selectedFilter = key }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(label)
                    }
                }
            }
        },
        confirmButton = {
            Button(
                onClick = { onApplyFilter(selectedFilter) }
            ) {
                Text("Apply")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text("Cancel")
            }
        }
    )
}

private fun formatTimestamp(timestamp: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        val outputFormat = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
        val date = inputFormat.parse(timestamp)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        timestamp
    }
}

private fun formatDuration(durationSeconds: Int): String {
    val minutes = durationSeconds / 60
    val seconds = durationSeconds % 60
    return String.format("%d:%02d", minutes, seconds)
}
