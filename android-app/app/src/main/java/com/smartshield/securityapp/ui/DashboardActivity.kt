package com.smartshield.securityapp.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import com.smartshield.securityapp.ui.components.*
import com.smartshield.securityapp.managers.SecurityServiceManager
import com.smartshield.securityapp.services.MediaCaptureService
import com.smartshield.securityapp.services.SOSService
import timber.log.Timber

class DashboardActivity : BaseActivity() {

    private lateinit var securityServiceManager: SecurityServiceManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize security service manager
        securityServiceManager = SecurityServiceManager(this)

        // Start security services
        securityServiceManager.startAllServices()

        setContent {
            SmartShieldTheme {
                DashboardScreen(
                    onLogout = {
                        navigateToLogin()
                    },
                    onNavigateToSettings = {
                        navigateToSettings()
                    },
                    securityServiceManager = securityServiceManager
                )
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Don't stop services when activity is destroyed
        // Services should continue running in background
    }

    private fun navigateToLogin() {
        // Clear login state
        val sharedPrefs = getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putBoolean("is_logged_in", false)
            .apply()

        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun navigateToSettings() {
        Timber.d("Settings clicked - not implemented yet")
        // TODO: Implement settings activity
    }
}

@Composable
fun DashboardScreen(
    onLogout: () -> Unit,
    onNavigateToSettings: () -> Unit,
    securityServiceManager: SecurityServiceManager
) {
    val context = LocalContext.current
    var showLogoutDialog by remember { mutableStateOf(false) }

    // Get user info from preferences
    val sharedPrefs = context.getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
    val userName = sharedPrefs.getString("user_name", "User") ?: "User"
    val selectedPlan = sharedPrefs.getString("selected_plan", "free") ?: "free"

    Box(modifier = Modifier.fillMaxSize()) {
        // Cyberpunk animated background
        CyberpunkBackground(
            modifier = Modifier.fillMaxSize(),
            showNetworkLines = true,
            showParticles = false,
            showGlowEffects = true
        )

        Scaffold(
            containerColor = Color.Transparent,
            topBar = {
                TopAppBar(
                    title = {
                        Column {
                            Text(
                                text = "SMART SHIELD",
                                style = MaterialTheme.typography.titleLarge,
                                color = SmartShieldColors.Primary,
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "// CONNECTED - $userName",
                                style = MaterialTheme.typography.bodySmall,
                                color = SmartShieldColors.Secondary,
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        }
                    },
                actions = {
                    // Professional Plan Badge
                    StatusBadge(
                        text = selectedPlan.uppercase(),
                        status = when (selectedPlan) {
                            "pro" -> StatusType.Success
                            "enterprise" -> StatusType.Info
                            "basic" -> StatusType.Warning
                            else -> StatusType.Neutral
                        },
                        size = BadgeSize.Small
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    IconButton(onClick = onNavigateToSettings) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "Settings",
                            tint = SmartShieldColors.OnPrimary
                        )
                    }
                    IconButton(onClick = { showLogoutDialog = true }) {
                        Icon(
                            imageVector = Icons.Default.ExitToApp,
                            contentDescription = "Logout",
                            tint = SmartShieldColors.OnPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SmartShieldColors.CardBackground.copy(alpha = 0.9f),
                    titleContentColor = SmartShieldColors.Primary,
                    actionIconContentColor = SmartShieldColors.Primary
                )
            )
        }
    ) { paddingValues ->

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            verticalArrangement = Arrangement.spacedBy(24.dp),
            contentPadding = PaddingValues(20.dp)
        ) {
            // Cyberpunk Device Status Overview
            item {
                Text(
                    text = "// DEVICE STATUS",
                    style = MaterialTheme.typography.headlineSmall,
                    color = SmartShieldColors.Primary,
                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Cyberpunk Power Control Center
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Status displays
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        CyberpunkStatusDisplay(
                            title = "GPS LOCK",
                            value = "ACTIVE",
                            isOnline = true,
                            modifier = Modifier.width(100.dp)
                        )
                        CyberpunkStatusDisplay(
                            title = "SIM CARD",
                            value = "OK",
                            isOnline = true,
                            modifier = Modifier.width(100.dp)
                        )
                    }

                    // Central power button
                    CyberpunkPowerButton(
                        isActive = true,
                        onToggle = {
                            // Toggle monitoring
                            Timber.d("Power button toggled")
                        },
                        buttonSize = 140f
                    )

                    // More status displays
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        CyberpunkStatusDisplay(
                            title = "BATTERY",
                            value = "85%",
                            isOnline = true,
                            modifier = Modifier.width(100.dp)
                        )
                        CyberpunkStatusDisplay(
                            title = "NETWORK",
                            value = "5G",
                            isOnline = true,
                            modifier = Modifier.width(100.dp)
                        )
                    }
                }
            }

            // Professional Smart Shield Features
            item {
                Text(
                    text = "Smart Shield Features",
                    style = MaterialTheme.typography.headlineSmall,
                    color = SmartShieldColors.OnBackground
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Core Features Grid with Professional Design
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.height(420.dp)
                ) {
                    items(getCoreFeatures()) { feature ->
                        ProfessionalFeatureCard(
                            feature = feature,
                            onClick = {
                                handleFeatureClick(feature.title, securityServiceManager, context)
                            }
                        )
                    }
                }
            }

            // Professional Advanced Features
            item {
                Text(
                    text = "Advanced Features",
                    style = MaterialTheme.typography.headlineSmall,
                    color = SmartShieldColors.OnBackground
                )

                Spacer(modifier = Modifier.height(16.dp))

                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    modifier = Modifier.height(340.dp)
                ) {
                    items(getAdvancedFeatures()) { feature ->
                        ProfessionalFeatureCard(
                            feature = feature,
                            onClick = {
                                handleFeatureClick(feature.title, securityServiceManager, context)
                            }
                        )
                    }
                }
            }

            // Professional Security Status
            item {
                Text(
                    text = "Security Status",
                    style = MaterialTheme.typography.headlineSmall,
                    color = SmartShieldColors.OnBackground
                )

                Spacer(modifier = Modifier.height(16.dp))

                SmartShieldCard(
                    modifier = Modifier.fillMaxWidth(),
                    backgroundColor = SmartShieldColors.Success.copy(alpha = 0.08f),
                    borderColor = SmartShieldColors.Success.copy(alpha = 0.2f),
                    borderWidth = 1.dp,
                    elevation = 6.dp
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Shield,
                            contentDescription = "Security",
                            tint = SmartShieldColors.Success,
                            modifier = Modifier.size(32.dp)
                        )
                        Spacer(modifier = Modifier.width(16.dp))
                        Column {
                            Text(
                                text = "All Systems Secure",
                                style = MaterialTheme.typography.titleMedium,
                                color = SmartShieldColors.Success
                            )
                            Text(
                                text = "Device monitoring active • Last scan: 2 min ago",
                                style = MaterialTheme.typography.bodyMedium,
                                color = SmartShieldColors.OnSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }

    } // Close Box

    // Logout Confirmation Dialog
    if (showLogoutDialog) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog = false },
            title = { Text("Logout") },
            text = { Text("Are you sure you want to logout?") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showLogoutDialog = false
                        Timber.d("User logged out")
                        onLogout()
                    }
                ) {
                    Text("Logout")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showLogoutDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}

data class DashboardFeature(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val color: Color,
    val isLocked: Boolean = false
)

@Composable
fun ProfessionalStatusCard(
    title: String,
    value: String,
    icon: ImageVector,
    status: StatusType,
    modifier: Modifier = Modifier
) {
    val statusColor = when (status) {
        StatusType.Success -> SmartShieldColors.Success
        StatusType.Warning -> SmartShieldColors.Warning
        StatusType.Error -> SmartShieldColors.Error
        StatusType.Info -> SmartShieldColors.Info
        StatusType.Neutral -> SmartShieldColors.Gray500
    }

    SmartShieldCard(
        modifier = modifier.height(110.dp),
        backgroundColor = statusColor.copy(alpha = 0.08f),
        borderColor = statusColor.copy(alpha = 0.2f),
        borderWidth = 1.dp,
        elevation = 6.dp
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = statusColor,
                modifier = Modifier.size(28.dp)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = value,
                style = MaterialTheme.typography.titleLarge,
                color = statusColor
            )
            Text(
                text = title,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center,
                color = SmartShieldColors.OnSurfaceVariant
            )
        }
    }
}

@Composable
fun ProfessionalFeatureCard(
    feature: DashboardFeature,
    onClick: () -> Unit
) {
    val statusColor = when {
        feature.isLocked -> SmartShieldColors.Gray400
        else -> when (feature.color) {
            Color(0xFF4CAF50) -> SmartShieldColors.Success
            Color(0xFF2196F3) -> SmartShieldColors.Info
            Color(0xFFFF9800) -> SmartShieldColors.Warning
            Color(0xFFF44336) -> SmartShieldColors.Error
            else -> SmartShieldColors.Primary
        }
    }

    SmartShieldCard(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f),
        onClick = if (!feature.isLocked) onClick else null,
        backgroundColor = if (feature.isLocked) {
            SmartShieldColors.Gray100
        } else {
            statusColor.copy(alpha = 0.08f)
        },
        borderColor = if (feature.isLocked) {
            SmartShieldColors.Gray300
        } else {
            statusColor.copy(alpha = 0.2f)
        },
        borderWidth = 1.dp,
        elevation = if (feature.isLocked) 2.dp else 6.dp
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Box {
                Icon(
                    imageVector = feature.icon,
                    contentDescription = feature.title,
                    tint = if (feature.isLocked) SmartShieldColors.Gray400 else statusColor,
                    modifier = Modifier.size(32.dp)
                )

                if (feature.isLocked) {
                    Icon(
                        imageVector = Icons.Default.Lock,
                        contentDescription = "Locked",
                        tint = SmartShieldColors.Gray500,
                        modifier = Modifier
                            .size(16.dp)
                            .align(Alignment.TopEnd)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = feature.title,
                style = MaterialTheme.typography.titleSmall,
                textAlign = TextAlign.Center,
                color = if (feature.isLocked) SmartShieldColors.Gray500 else SmartShieldColors.OnSurface
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = feature.description,
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center,
                color = if (feature.isLocked) SmartShieldColors.Gray400 else SmartShieldColors.OnSurfaceVariant,
                maxLines = 2
            )
        }
    }
}

@Composable
fun QuickActionCard(
    title: String,
    icon: ImageVector,
    color: Color,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .width(100.dp)
            .height(80.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = color.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = title,
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
fun ActivityItem(
    title: String,
    description: String,
    time: String,
    icon: ImageVector
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp
            )
            Text(
                text = description,
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
        Text(
            text = time,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
        )
    }
}

@Composable
fun FeatureCard(
    feature: DashboardFeature,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = feature.color.copy(alpha = 0.1f)
        ),
        border = androidx.compose.foundation.BorderStroke(
            1.dp,
            feature.color.copy(alpha = 0.3f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(12.dp)
            ) {
                Box {
                    Icon(
                        imageVector = feature.icon,
                        contentDescription = null,
                        tint = feature.color,
                        modifier = Modifier.size(32.dp)
                    )

                    if (feature.isLocked) {
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Locked",
                            tint = Color(0xFFFF9800),
                            modifier = Modifier
                                .size(16.dp)
                                .offset(x = 20.dp, y = (-8).dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = feature.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = feature.color,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )

                Text(
                    text = feature.description,
                    fontSize = 10.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    maxLines = 2
                )
            }
        }
    }
}

fun getCoreFeatures(): List<DashboardFeature> {
    return listOf(
        DashboardFeature(
            title = "Live Tracking",
            description = "Real-time GPS location",
            icon = Icons.Default.LocationOn,
            color = Color(0xFF43A047)
        ),
        DashboardFeature(
            title = "Media Gallery",
            description = "Photos, videos & audio",
            icon = Icons.Default.PhotoLibrary,
            color = Color(0xFFE91E63)
        ),
        DashboardFeature(
            title = "Device Control",
            description = "Remote management",
            icon = Icons.Default.Devices,
            color = Color(0xFF9C27B0)
        ),
        DashboardFeature(
            title = "SOS Alerts",
            description = "Emergency notifications",
            icon = Icons.Default.Emergency,
            color = Color(0xFFF44336)
        ),
        DashboardFeature(
            title = "Parental Controls",
            description = "Child safety features",
            icon = Icons.Default.FamilyRestroom,
            color = Color(0xFFFF5722)
        ),
        DashboardFeature(
            title = "Settings",
            description = "App configuration",
            icon = Icons.Default.Settings,
            color = Color(0xFF607D8B)
        )
    )
}

fun getAdvancedFeatures(): List<DashboardFeature> {
    return listOf(
        DashboardFeature(
            title = "Trackers",
            description = "Pets, cars & AirTags",
            icon = Icons.Default.Pets,
            color = Color(0xFF795548)
        ),
        DashboardFeature(
            title = "Remote Shield",
            description = "Screen sharing & control",
            icon = Icons.Default.ScreenShare,
            color = Color(0xFF2E7D32)
        ),
        DashboardFeature(
            title = "AI Protect",
            description = "Smart security agent",
            icon = Icons.Default.Psychology,
            color = Color(0xFF6366F1)
        ),
        DashboardFeature(
            title = "Analytics",
            description = "Usage statistics",
            icon = Icons.Default.Analytics,
            color = Color(0xFFFF9800)
        )
    )
}

fun handleFeatureClick(featureTitle: String, securityServiceManager: SecurityServiceManager, context: Context) {
    when (featureTitle) {
        "Live Tracking" -> {
            navigateToLocationHistory(context)
            securityServiceManager.startLocationTracking()
            Timber.d("Location tracking started")
        }
        "Media Gallery" -> {
            navigateToMediaGallery(context)
            Timber.d("Media gallery opened")
        }
        "Device Control" -> {
            // TODO: Navigate to device control screen
            Timber.d("Device control clicked")
        }
        "SOS Alerts" -> {
            securityServiceManager.triggerSOS("Manual SOS from dashboard")
            Timber.d("SOS triggered from dashboard")
        }
        "Parental Controls" -> {
            // TODO: Navigate to parental controls
            Timber.d("Parental controls clicked")
        }
        "Settings" -> {
            navigateToAdvancedSettings(context)
            Timber.d("Settings opened")
        }
        "Trackers" -> {
            // TODO: Navigate to trackers
            Timber.d("Trackers clicked")
        }
        "Remote Shield" -> {
            // TODO: Navigate to remote control
            Timber.d("Remote Shield clicked")
        }
        "AI Protect" -> {
            // TODO: Navigate to AI protection
            Timber.d("AI Protect clicked")
        }
        "Analytics" -> {
            // TODO: Navigate to analytics
            Timber.d("Analytics clicked")
        }
        else -> {
            Timber.d("Unknown feature clicked: $featureTitle")
        }
    }
}

fun navigateToLocationHistory(context: Context) {
    val intent = Intent(context, LocationHistoryActivity::class.java)
    context.startActivity(intent)
}

fun navigateToMediaGallery(context: Context) {
    val intent = Intent(context, MediaGalleryActivity::class.java)
    context.startActivity(intent)
}

fun navigateToAdvancedSettings(context: Context) {
    val intent = Intent(context, AdvancedSettingsActivity::class.java)
    context.startActivity(intent)
}
