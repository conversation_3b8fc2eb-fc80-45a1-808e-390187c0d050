package com.smartshield.securityapp.utils

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import timber.log.Timber
import java.util.*

/**
 * Smart Shield Language Manager
 * Handles language switching and locale management for the entire app
 */
object LanguageManager {
    
    private const val PREF_SELECTED_LANGUAGE = "selected_language"
    private const val PREF_LANGUAGE_SELECTED = "language_selected"
    
    /**
     * Supported languages with their details
     */
    data class SupportedLanguage(
        val code: String,
        val name: String,
        val nativeName: String,
        val flagEmoji: String,
        val isRtl: Boolean = false
    )
    
    val supportedLanguages = listOf(
        // Popular languages first
        SupportedLanguage("en", "English", "English", "🇺🇸"),
        SupportedLanguage("es", "Spanish", "Español", "🇪🇸"),
        SupportedLanguage("zh", "Chinese", "中文", "🇨🇳"),
        SupportedLanguage("hi", "Hindi", "हिन्दी", "🇮🇳"),
        SupportedLanguage("ar", "Arabic", "العربية", "🇲🇦", isRtl = true),
        SupportedLanguage("pt", "Portuguese", "Português", "🇧🇷"),
        SupportedLanguage("ru", "Russian", "Русский", "🇷🇺"),
        SupportedLanguage("ja", "Japanese", "日本語", "🇯🇵"),
        SupportedLanguage("fr", "French", "Français", "🇫🇷"),
        SupportedLanguage("de", "German", "Deutsch", "🇩🇪"),
        SupportedLanguage("ko", "Korean", "한국어", "🇰🇷"),
        SupportedLanguage("it", "Italian", "Italiano", "🇮🇹"),
        SupportedLanguage("tr", "Turkish", "Türkçe", "🇹🇷"),
        SupportedLanguage("nl", "Dutch", "Nederlands", "🇳🇱"),
        SupportedLanguage("pl", "Polish", "Polski", "🇵🇱"),
        SupportedLanguage("uk", "Ukrainian", "Українська", "🇺🇦"),
        SupportedLanguage("sv", "Swedish", "Svenska", "🇸🇪"),
        SupportedLanguage("no", "Norwegian", "Norsk", "🇳🇴"),
        SupportedLanguage("da", "Danish", "Dansk", "🇩🇰"),
        SupportedLanguage("fi", "Finnish", "Suomi", "🇫🇮"),
        SupportedLanguage("cs", "Czech", "Čeština", "🇨🇿"),
        SupportedLanguage("hu", "Hungarian", "Magyar", "🇭🇺"),
        SupportedLanguage("ro", "Romanian", "Română", "🇷🇴"),
        SupportedLanguage("bg", "Bulgarian", "Български", "🇧🇬"),
        SupportedLanguage("hr", "Croatian", "Hrvatski", "🇭🇷"),
        SupportedLanguage("sk", "Slovak", "Slovenčina", "🇸🇰"),
        SupportedLanguage("sl", "Slovenian", "Slovenščina", "🇸🇮"),
        SupportedLanguage("et", "Estonian", "Eesti", "🇪🇪"),
        SupportedLanguage("lv", "Latvian", "Latviešu", "🇱🇻"),
        SupportedLanguage("lt", "Lithuanian", "Lietuvių", "🇱🇹"),
        SupportedLanguage("id", "Indonesian", "Bahasa Indonesia", "🇮🇩"),
        SupportedLanguage("ms", "Malay", "Bahasa Melayu", "🇲🇾"),
        SupportedLanguage("th", "Thai", "ไทย", "🇹🇭"),
        SupportedLanguage("vi", "Vietnamese", "Tiếng Việt", "🇻🇳"),
        SupportedLanguage("bn", "Bengali", "বাংলা", "🇧🇩"),
        SupportedLanguage("ur", "Urdu", "اردو", "🇵🇰", isRtl = true),
        SupportedLanguage("fa", "Persian", "فارسی", "🇮🇷", isRtl = true),
        SupportedLanguage("fil", "Filipino", "Filipino", "🇵🇭"),
        SupportedLanguage("sw", "Swahili", "Kiswahili", "🇰🇪"),
        SupportedLanguage("am", "Amharic", "አማርኛ", "🇪🇹"),
        SupportedLanguage("zu", "Zulu", "isiZulu", "🇿🇦"),
        SupportedLanguage("af", "Afrikaans", "Afrikaans", "🇿🇦"),
        SupportedLanguage("is", "Icelandic", "Íslenska", "🇮🇸"),
        SupportedLanguage("mt", "Maltese", "Malti", "🇲🇹"),
        SupportedLanguage("ga", "Irish", "Gaeilge", "🇮🇪")
    )
    
    /**
     * Auto-detect device language
     */
    fun autoDetectLanguage(): String {
        val deviceLanguage = Locale.getDefault().language
        return if (supportedLanguages.any { it.code == deviceLanguage }) {
            deviceLanguage
        } else {
            "en" // Default to English
        }
    }
    
    /**
     * Get saved language preference
     */
    fun getSavedLanguage(context: Context): String {
        return try {
            val prefs = context.getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
            prefs.getString(PREF_SELECTED_LANGUAGE, autoDetectLanguage()) ?: "en"
        } catch (e: Exception) {
            Timber.e(e, "Failed to get saved language")
            "en" // Default to English
        }
    }

    /**
     * Save language preference
     */
    fun saveLanguage(context: Context, languageCode: String) {
        try {
            val prefs = context.getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
            prefs.edit()
                .putString(PREF_SELECTED_LANGUAGE, languageCode)
                .putBoolean(PREF_LANGUAGE_SELECTED, true)
                .apply()
            Timber.d("Language saved: $languageCode")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save language: $languageCode")
        }
    }
    
    /**
     * Check if language has been selected by user
     */
    fun isLanguageSelected(context: Context): Boolean {
        val prefs = context.getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
        return prefs.getBoolean(PREF_LANGUAGE_SELECTED, false)
    }
    
    /**
     * Apply language to context
     */
    fun applyLanguage(context: Context, languageCode: String): Context {
        return try {
            val locale = Locale(languageCode)
            Locale.setDefault(locale)

            val config = Configuration(context.resources.configuration)
            config.setLocale(locale)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                context.createConfigurationContext(config)
            } else {
                @Suppress("DEPRECATION")
                context.resources.updateConfiguration(config, context.resources.displayMetrics)
                context
            }
        } catch (e: Exception) {
            // Return original context if language application fails
            context
        }
    }
    
    /**
     * Get language details by code
     */
    fun getLanguageByCode(code: String): SupportedLanguage? {
        return supportedLanguages.find { it.code == code }
    }
    
    /**
     * Check if language is RTL
     */
    fun isRtlLanguage(languageCode: String): Boolean {
        return getLanguageByCode(languageCode)?.isRtl ?: false
    }
    
    /**
     * Get popular languages (most commonly used - top 20)
     */
    fun getPopularLanguages(): List<SupportedLanguage> {
        val popularCodes = listOf("en", "es", "zh", "hi", "ar", "pt", "ru", "ja", "fr", "de", "ko", "it", "tr", "nl", "pl", "uk", "sv", "no", "da", "fi")
        return supportedLanguages.filter { it.code in popularCodes }
    }
}
