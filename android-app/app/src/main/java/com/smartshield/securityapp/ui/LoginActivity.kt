package com.smartshield.securityapp.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.RemoveRedEye
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.smartshield.securityapp.R
import com.smartshield.securityapp.ui.components.CyberpunkBackground
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import timber.log.Timber

class LoginActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            SmartShieldTheme {
                LoginScreen(
                    onLoginSuccess = {
                        navigateToDashboard()
                    }
                )
            }
        }
    }

    private fun navigateToDashboard() {
        // Save login state
        val sharedPrefs = getSharedPreferences("smart_shield_prefs", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putBoolean("is_logged_in", true)
            .apply()

        val intent = Intent(this, DashboardActivity::class.java)
        startActivity(intent)
        finish()
    }
}

@Composable
fun LoginScreen(
    onLoginSuccess: () -> Unit
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    val context = LocalContext.current

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // Cyberpunk animated background
        CyberpunkBackground(
            modifier = Modifier.fillMaxSize(),
            showNetworkLines = true,
            showParticles = true,
            showGlowEffects = true
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            // Cyberpunk Logo and Title
            Box(
                modifier = Modifier
                    .size(140.dp)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                SmartShieldColors.Primary.copy(alpha = 0.3f),
                                SmartShieldColors.PrimaryGlow,
                                Color.Transparent
                            ),
                            radius = 200f
                        ),
                        shape = RoundedCornerShape(70.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "🛡️",
                    fontSize = 64.sp,
                    modifier = Modifier
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    SmartShieldColors.GlowEffect,
                                    Color.Transparent
                                ),
                                radius = 100f
                            ),
                            shape = RoundedCornerShape(50.dp)
                        )
                        .padding(16.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = stringResource(R.string.smart_shield),
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = SmartShieldColors.Primary,
                textAlign = TextAlign.Center,
                style = androidx.compose.ui.text.TextStyle(
                    shadow = Shadow(
                        color = SmartShieldColors.PrimaryGlow,
                        offset = Offset(0f, 0f),
                        blurRadius = 20f
                    )
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "// ${stringResource(R.string.status_online).uppercase()}",
                fontSize = 14.sp,
                color = SmartShieldColors.Secondary,
                textAlign = TextAlign.Center,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                letterSpacing = 2.sp
            )

            Spacer(modifier = Modifier.height(48.dp))

            // Cyberpunk Login Form
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp)),
                colors = CardDefaults.cardColors(
                    containerColor = SmartShieldColors.CardBackground.copy(alpha = 0.9f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
                border = androidx.compose.foundation.BorderStroke(
                    1.dp,
                    SmartShieldColors.Primary.copy(alpha = 0.3f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Text(
                        text = stringResource(R.string.welcome_to),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = SmartShieldColors.Primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.please_enter_credentials),
                        fontSize = 14.sp,
                        color = SmartShieldColors.OnSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // Error Message
                    if (errorMessage.isNotEmpty()) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = SmartShieldColors.Error.copy(alpha = 0.1f)
                            ),
                            border = androidx.compose.foundation.BorderStroke(
                                1.dp,
                                SmartShieldColors.Error.copy(alpha = 0.3f)
                            )
                        ) {
                            Text(
                                text = errorMessage,
                                modifier = Modifier.padding(12.dp),
                                color = SmartShieldColors.Error,
                                fontSize = 14.sp
                            )
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    // Cyberpunk Email Field
                    OutlinedTextField(
                        value = email,
                        onValueChange = { email = it },
                        label = {
                            Text(
                                stringResource(R.string.email),
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Email,
                                contentDescription = "Email",
                                tint = SmartShieldColors.Primary
                            )
                        },
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Email
                        ),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isLoading,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = SmartShieldColors.Primary,
                            unfocusedBorderColor = SmartShieldColors.Primary.copy(alpha = 0.3f),
                            focusedLabelColor = SmartShieldColors.Primary,
                            unfocusedLabelColor = SmartShieldColors.OnSurfaceVariant,
                            cursorColor = SmartShieldColors.Primary,
                            focusedTextColor = SmartShieldColors.OnSurface,
                            unfocusedTextColor = SmartShieldColors.OnSurfaceVariant
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Cyberpunk Password Field
                    OutlinedTextField(
                        value = password,
                        onValueChange = { password = it },
                        label = {
                            Text(
                                stringResource(R.string.password),
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Lock,
                                contentDescription = "Password",
                                tint = SmartShieldColors.Primary
                            )
                        },
                        trailingIcon = {
                            IconButton(
                                onClick = { passwordVisible = !passwordVisible }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.RemoveRedEye,
                                    contentDescription = if (passwordVisible)
                                        stringResource(R.string.hide_password)
                                    else
                                        stringResource(R.string.show_password),
                                    tint = SmartShieldColors.Primary.copy(alpha = 0.7f)
                                )
                            }
                        },
                        visualTransformation = if (passwordVisible)
                            VisualTransformation.None
                        else
                            PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Password
                        ),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isLoading,
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = SmartShieldColors.Primary,
                            unfocusedBorderColor = SmartShieldColors.Primary.copy(alpha = 0.3f),
                            focusedLabelColor = SmartShieldColors.Primary,
                            unfocusedLabelColor = SmartShieldColors.OnSurfaceVariant,
                            cursorColor = SmartShieldColors.Primary,
                            focusedTextColor = SmartShieldColors.OnSurface,
                            unfocusedTextColor = SmartShieldColors.OnSurfaceVariant
                        )
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // Cyberpunk Login Button
                    Button(
                        onClick = {
                            if (email.isNotBlank() && password.isNotBlank()) {
                                isLoading = true
                                // Simple demo login
                                Timber.d("Login attempt: $email")
                                onLoginSuccess()
                            } else {
                                errorMessage = context.getString(R.string.please_fill_all_fields)
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        SmartShieldColors.Primary,
                                        SmartShieldColors.Secondary
                                    )
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ),
                        enabled = !isLoading && email.isNotBlank() && password.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Transparent,
                            disabledContainerColor = SmartShieldColors.Disabled
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        if (isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                color = SmartShieldColors.OnPrimary
                            )
                        } else {
                            Text(
                                text = stringResource(R.string.login).uppercase(),
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = SmartShieldColors.OnPrimary,
                                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Cyberpunk Demo Login Button
                    OutlinedButton(
                        onClick = {
                            email = "<EMAIL>"
                            password = "demo123"
                            isLoading = true
                            Timber.d("Demo login")
                            onLoginSuccess()
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isLoading,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = SmartShieldColors.Primary
                        ),
                        border = androidx.compose.foundation.BorderStroke(
                            1.dp,
                            SmartShieldColors.Primary.copy(alpha = 0.5f)
                        )
                    ) {
                        Text(
                            text = stringResource(R.string.demo_login).uppercase(),
                            color = SmartShieldColors.Primary,
                            fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Cyberpunk Footer
            Text(
                text = "03:29",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = SmartShieldColors.Primary,
                textAlign = TextAlign.Center,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = stringResource(R.string.secure_encrypted_protected).uppercase(),
                color = SmartShieldColors.OnSurfaceVariant,
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                letterSpacing = 1.sp
            )
        }
    }
}
