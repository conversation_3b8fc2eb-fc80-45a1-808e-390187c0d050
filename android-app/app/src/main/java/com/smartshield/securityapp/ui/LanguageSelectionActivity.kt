package com.smartshield.securityapp.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.ui.draw.scale
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Language
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.smartshield.securityapp.R
import com.smartshield.securityapp.ui.theme.SmartShieldTheme
import com.smartshield.securityapp.ui.theme.SmartShieldColors
import com.smartshield.securityapp.ui.components.*
import com.smartshield.securityapp.utils.LanguageManager
import kotlinx.coroutines.delay
import timber.log.Timber
import java.util.*

class LanguageSelectionActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            SmartShieldTheme {
                LanguageSelectionScreen(
                    onLanguageSelected = { languageCode ->
                        saveSelectedLanguage(languageCode)
                        navigateToFeatureShowcase()
                    }
                )
            }
        }
    }

    private fun saveSelectedLanguage(languageCode: String) {
        try {
            LanguageManager.saveLanguage(this, languageCode)
            Timber.d("Language selected and saved: $languageCode")
        } catch (e: Exception) {
            Timber.e(e, "Failed to save language: $languageCode")
        }
    }

    private fun navigateToFeatureShowcase() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }
}



@Composable
fun LanguageSelectionScreen(
    onLanguageSelected: (String) -> Unit
) {
    val context = LocalContext.current

    // Auto-detect device language using LanguageManager
    val deviceLanguage = remember {
        LanguageManager.getSavedLanguage(context)
    }

    var selectedLanguage by remember { mutableStateOf(deviceLanguage) }
    var isExpanded by remember { mutableStateOf(false) }
    var showContent by remember { mutableStateOf(false) }

    // Complete language list - all 46 languages as requested
    val allLanguages = remember {
        listOf(
            // Top 10 Most Popular
            Language("en", "English", "English", "🇺🇸", isPopular = true),
            Language("es", "Spanish", "Español", "🇪🇸", isPopular = true),
            Language("zh", "Chinese", "中文", "🇨🇳", isPopular = true),
            Language("hi", "Hindi", "हिन्दी", "🇮🇳", isPopular = true),
            Language("ar", "Arabic", "العربية", "🇲🇦", isPopular = true, isRtl = true),
            Language("pt", "Portuguese", "Português", "🇧🇷", isPopular = true),
            Language("ru", "Russian", "Русский", "🇷🇺", isPopular = true),
            Language("ja", "Japanese", "日本語", "🇯🇵", isPopular = true),
            Language("fr", "French", "Français", "🇫🇷", isPopular = true),
            Language("de", "German", "Deutsch", "🇩🇪", isPopular = true),

            // 11-20 European Languages
            Language("ko", "Korean", "한국어", "🇰🇷", isPopular = true),
            Language("it", "Italian", "Italiano", "🇮🇹", isPopular = true),
            Language("tr", "Turkish", "Türkçe", "🇹🇷", isPopular = true),
            Language("nl", "Dutch", "Nederlands", "🇳🇱", isPopular = true),
            Language("pl", "Polish", "Polski", "🇵🇱", isPopular = true),
            Language("uk", "Ukrainian", "Українська", "🇺🇦", isPopular = true),
            Language("sv", "Swedish", "Svenska", "🇸🇪", isPopular = true),
            Language("no", "Norwegian", "Norsk", "🇳🇴", isPopular = true),
            Language("da", "Danish", "Dansk", "🇩🇰", isPopular = true),
            Language("fi", "Finnish", "Suomi", "🇫🇮", isPopular = true),

            // 21-30 Central/Eastern European
            Language("cs", "Czech", "Čeština", "🇨🇿"),
            Language("hu", "Hungarian", "Magyar", "🇭🇺"),
            Language("ro", "Romanian", "Română", "🇷🇴"),
            Language("bg", "Bulgarian", "Български", "🇧🇬"),
            Language("hr", "Croatian", "Hrvatski", "🇭🇷"),
            Language("sk", "Slovak", "Slovenčina", "🇸🇰"),
            Language("sl", "Slovenian", "Slovenščina", "🇸🇮"),
            Language("et", "Estonian", "Eesti", "🇪🇪"),
            Language("lv", "Latvian", "Latviešu", "🇱🇻"),
            Language("lt", "Lithuanian", "Lietuvių", "🇱🇹"),

            // 31-40 Asian & Regional
            Language("id", "Indonesian", "Bahasa Indonesia", "🇮🇩"),
            Language("ms", "Malay", "Bahasa Melayu", "🇲🇾"),
            Language("th", "Thai", "ไทย", "🇹🇭"),
            Language("vi", "Vietnamese", "Tiếng Việt", "🇻🇳"),
            Language("bn", "Bengali", "বাংলা", "🇧🇩"),
            Language("ur", "Urdu", "اردو", "🇵🇰", isRtl = true),
            Language("fa", "Persian", "فارسی", "🇮🇷", isRtl = true),
            Language("fil", "Filipino", "Filipino", "🇵🇭"),
            Language("sw", "Swahili", "Kiswahili", "🇰🇪"),

            // 41-46 Additional Languages
            Language("am", "Amharic", "አማርኛ", "🇪🇹"),
            Language("zu", "Zulu", "isiZulu", "🇿🇦"),
            Language("af", "Afrikaans", "Afrikaans", "🇿🇦"),
            Language("is", "Icelandic", "Íslenska", "🇮🇸"),
            Language("mt", "Maltese", "Malti", "🇲🇹"),
            Language("ga", "Irish", "Gaeilge", "🇮🇪")
        )
    }

    // Get selected language details
    val selectedLanguageDetails = remember(selectedLanguage) {
        allLanguages.find { it.code == selectedLanguage } ?: allLanguages.first()
    }

    // Animation states
    val listState = rememberLazyListState()

    // Entrance animation
    LaunchedEffect(Unit) {
        delay(300)
        showContent = true
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        SmartShieldColors.Primary.copy(alpha = 0.1f),
                        SmartShieldColors.Background,
                        SmartShieldColors.Background
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(40.dp)) // Reduced from 60dp

            // Welcome Title
            AnimatedVisibility(
                visible = showContent,
                enter = slideInVertically(
                    initialOffsetY = { -it / 2 },
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeIn(animationSpec = tween(600))
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(R.string.welcome_to),
                        style = MaterialTheme.typography.headlineSmall,
                        color = SmartShieldColors.OnSurfaceVariant,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.smart_shield),
                        style = MaterialTheme.typography.headlineLarge,
                        color = SmartShieldColors.Primary,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                }
            }

            Spacer(modifier = Modifier.height(60.dp)) // Fixed spacer instead of weight to position at 3/4

            // Central House with Language Selection (positioned at 3/4 of screen)
            AnimatedVisibility(
                visible = showContent,
                enter = scaleIn(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeIn(animationSpec = tween(800, delayMillis = 400))
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Professional House with Enhanced Flag Circle
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(140.dp)
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = rememberRipple(
                                    color = SmartShieldColors.Primary,
                                    radius = 70.dp
                                )
                            ) { isExpanded = !isExpanded }
                    ) {
                        // Animated Background Circle
                        val animatedScale by animateFloatAsState(
                            targetValue = if (isExpanded) 1.1f else 1f,
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        )

                        // Main House Container
                        SmartShieldCard(
                            modifier = Modifier
                                .size(120.dp)
                                .scale(animatedScale),
                            elevation = 16.dp,
                            backgroundColor = SmartShieldColors.Surface,
                            cornerRadius = 24.dp,
                            borderColor = SmartShieldColors.Primary.copy(alpha = 0.2f),
                            borderWidth = 2.dp
                        ) {
                            Column(
                                modifier = Modifier.fillMaxSize(),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                // Enhanced Flag Circle
                                Box(
                                    modifier = Modifier
                                        .size(56.dp)
                                        .shadow(8.dp, CircleShape)
                                        .background(
                                            brush = androidx.compose.ui.graphics.Brush.radialGradient(
                                                colors = listOf(
                                                    SmartShieldColors.Primary.copy(alpha = 0.1f),
                                                    SmartShieldColors.Primary.copy(alpha = 0.05f)
                                                )
                                            ),
                                            shape = CircleShape
                                        )
                                        .border(
                                            width = 3.dp,
                                            color = SmartShieldColors.Primary.copy(alpha = 0.3f),
                                            shape = CircleShape
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = selectedLanguageDetails.flagEmoji,
                                        fontSize = 28.sp,
                                        modifier = Modifier.scale(1.2f)
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                // Language Name with Better Typography
                                Text(
                                    text = selectedLanguageDetails.nativeName,
                                    style = MaterialTheme.typography.labelLarge,
                                    color = SmartShieldColors.OnSurface,
                                    fontWeight = FontWeight.Bold,
                                    textAlign = TextAlign.Center,
                                    maxLines = 1,
                                    fontSize = 12.sp
                                )
                            }
                        }

                        // Subtle Glow Effect
                        Box(
                            modifier = Modifier
                                .size(130.dp)
                                .background(
                                    brush = androidx.compose.ui.graphics.Brush.radialGradient(
                                        colors = listOf(
                                            SmartShieldColors.Primary.copy(alpha = 0.1f),
                                            androidx.compose.ui.graphics.Color.Transparent
                                        ),
                                        radius = 65f
                                    ),
                                    shape = CircleShape
                                )
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    // Tap to Select Text
                    Text(
                        text = stringResource(R.string.tap_house_select_language),
                        style = MaterialTheme.typography.bodyMedium,
                        color = SmartShieldColors.OnSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f)) // Fill remaining space to push content up
        }

        // Simplified Dropdown Menu - Better Phone Compatibility
        if (isExpanded) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter) // Position at bottom
                    .padding(horizontal = 16.dp, vertical = 80.dp) // More space from bottom
            ) {
            // Professional Transparent Dropdown Container - 60% Bigger
            SmartShieldCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(512.dp), // Increased from 320dp to 512dp (60% bigger)
                elevation = 24.dp,
                backgroundColor = Color.Black.copy(alpha = 0.85f), // More transparent
                cornerRadius = 20.dp,
                borderColor = SmartShieldColors.Primary.copy(alpha = 0.4f),
                borderWidth = 1.dp
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // Elegant Header with Close Button - Bigger
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp), // Increased padding
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.select_language),
                            style = MaterialTheme.typography.headlineSmall, // Bigger text
                            color = Color.White,
                            fontWeight = FontWeight.SemiBold
                        )

                        IconButton(
                            onClick = { isExpanded = false },
                            modifier = Modifier
                                .size(44.dp) // Bigger close button
                                .background(
                                    Color.White.copy(alpha = 0.1f),
                                    CircleShape
                                )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Close",
                                tint = Color.White.copy(alpha = 0.9f),
                                modifier = Modifier.size(22.dp) // Bigger icon
                            )
                        }
                    }

                    // Divider
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(1.dp)
                            .background(
                                Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        SmartShieldColors.Primary.copy(alpha = 0.3f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )

                    // Language Options in Vertical List - Show ALL languages with smooth scrolling
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 20.dp, vertical = 16.dp), // Increased padding
                        verticalArrangement = Arrangement.spacedBy(12.dp), // Increased spacing
                        state = rememberLazyListState() // Add list state for smooth scrolling
                    ) {
                        // Show ALL languages, not just popular ones
                        itemsIndexed(allLanguages) { index, language ->
                            ProfessionalLanguageDropdownItem(
                                language = language,
                                isSelected = selectedLanguage == language.code,
                                onSelect = {
                                    selectedLanguage = language.code
                                    isExpanded = false
                                },
                                animationDelay = 0, // Remove staggered animation for better scroll performance
                                isBigger = true // Pass flag for bigger size
                            )
                        }
                    }
                }
            }
            }
        }

        // Continue Button (Also outside Column scope)
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(24.dp)
        ) {

            AnimatedVisibility(
                visible = showContent,
                enter = slideInVertically(
                    initialOffsetY = { it / 3 },
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) + fadeIn(animationSpec = tween(800, delayMillis = 600))
            ) {
                SmartShieldButton(
                    text = stringResource(R.string.continue_button),
                    onClick = { onLanguageSelected(selectedLanguage) },
                    modifier = Modifier.fillMaxWidth(),
                    variant = ButtonVariant.Primary,
                    size = ButtonSize.Large,
                    icon = Icons.Default.ArrowForward,
                    iconPosition = IconPosition.End
                )
            }
        }
    }
}

@Composable
fun CompactLanguageItem(
    language: Language,
    isSelected: Boolean,
    onSelect: () -> Unit,
    animationDelay: Int = 0
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInHorizontally(
            initialOffsetX = { it / 2 },
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        ) + fadeIn(animationSpec = tween(300))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = rememberRipple(
                        color = SmartShieldColors.Primary
                    )
                ) { onSelect() }
                .background(
                    if (isSelected) {
                        SmartShieldColors.Primary.copy(alpha = 0.1f)
                    } else {
                        Color.Transparent
                    }
                )
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Enhanced Professional Flag Circle
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .shadow(4.dp, CircleShape)
                    .background(
                        brush = androidx.compose.ui.graphics.Brush.radialGradient(
                            colors = listOf(
                                SmartShieldColors.Primary.copy(alpha = 0.08f),
                                SmartShieldColors.SurfaceVariant
                            )
                        ),
                        shape = CircleShape
                    )
                    .border(
                        width = 2.dp,
                        color = if (isSelected) SmartShieldColors.Primary else SmartShieldColors.Primary.copy(alpha = 0.2f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = language.flagEmoji,
                    fontSize = 20.sp,
                    modifier = Modifier.scale(1.1f)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // Language Info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = language.name,
                    style = MaterialTheme.typography.bodyLarge,
                    color = if (isSelected) SmartShieldColors.Primary else SmartShieldColors.OnSurface,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
                )

                Text(
                    text = language.nativeName,
                    style = MaterialTheme.typography.bodySmall,
                    color = SmartShieldColors.OnSurfaceVariant
                )
            }

            // Selection Indicator
            AnimatedVisibility(
                visible = isSelected,
                enter = scaleIn(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessHigh
                    )
                ) + fadeIn(),
                exit = scaleOut() + fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            color = SmartShieldColors.Primary,
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = SmartShieldColors.OnPrimary,
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun ProfessionalLanguageDropdownItem(
    language: Language,
    isSelected: Boolean,
    onSelect: () -> Unit,
    animationDelay: Int = 0,
    isBigger: Boolean = false
) {
    // Simplified - no complex animations for better scroll performance
    Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = rememberRipple(
                        color = SmartShieldColors.Primary.copy(alpha = 0.3f)
                    )
                ) { onSelect() }
                .background(
                    if (isSelected) {
                        Brush.horizontalGradient(
                            colors = listOf(
                                SmartShieldColors.Primary.copy(alpha = 0.2f),
                                SmartShieldColors.Primary.copy(alpha = 0.1f)
                            )
                        )
                    } else {
                        Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Transparent
                            )
                        )
                    }
                )
                .padding(
                    horizontal = if (isBigger) 20.dp else 16.dp,
                    vertical = if (isBigger) 16.dp else 12.dp
                ), // Bigger padding when isBigger is true
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Professional Flag Circle with Glow Effect - Bigger
            Box(
                modifier = Modifier
                    .size(if (isBigger) 56.dp else 44.dp) // Bigger flag circle
                    .shadow(
                        elevation = if (isSelected) 8.dp else 4.dp,
                        shape = CircleShape
                    )
                    .background(
                        brush = Brush.radialGradient(
                            colors = if (isSelected) {
                                listOf(
                                    SmartShieldColors.Primary.copy(alpha = 0.15f),
                                    Color.White.copy(alpha = 0.95f)
                                )
                            } else {
                                listOf(
                                    Color.White.copy(alpha = 0.1f),
                                    Color.White.copy(alpha = 0.05f)
                                )
                            }
                        ),
                        shape = CircleShape
                    )
                    .border(
                        width = if (isSelected) 2.dp else 1.dp,
                        color = if (isSelected) {
                            SmartShieldColors.Primary.copy(alpha = 0.6f)
                        } else {
                            Color.White.copy(alpha = 0.2f)
                        },
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = language.flagEmoji,
                    fontSize = if (isBigger) 28.sp else 22.sp, // Bigger emoji
                    modifier = Modifier.scale(if (isSelected) 1.1f else 1.0f)
                )
            }

            Spacer(modifier = Modifier.width(if (isBigger) 20.dp else 16.dp)) // Bigger spacing

            // Language Information - Bigger text
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = language.nativeName,
                    style = if (isBigger) MaterialTheme.typography.titleLarge else MaterialTheme.typography.titleMedium, // Bigger text
                    color = if (isSelected) {
                        SmartShieldColors.Primary.copy(alpha = 0.9f)
                    } else {
                        Color.White.copy(alpha = 0.9f)
                    },
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium
                )

                Text(
                    text = language.name,
                    style = if (isBigger) MaterialTheme.typography.bodyMedium else MaterialTheme.typography.bodySmall, // Bigger text
                    color = Color.White.copy(alpha = 0.7f)
                )
            }

            // Selection Indicator with Animation
            AnimatedVisibility(
                visible = isSelected,
                enter = scaleIn(
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessHigh
                    )
                ) + fadeIn(),
                exit = scaleOut() + fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .size(if (isBigger) 36.dp else 28.dp) // Bigger selection indicator
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    SmartShieldColors.Primary,
                                    SmartShieldColors.Primary.copy(alpha = 0.8f)
                                )
                            ),
                            shape = CircleShape
                        )
                        .shadow(4.dp, CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = "Selected",
                        tint = Color.White,
                        modifier = Modifier.size(if (isBigger) 20.dp else 16.dp) // Bigger check icon
                    )
                }
            }
        }
    }

@Composable
fun AnimatedLanguageItem(
    language: Language,
    isSelected: Boolean,
    onSelect: () -> Unit,
    animationDelay: Int = 0,
    showPopularBadge: Boolean = false
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(animationDelay.toLong())
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInHorizontally(
            initialOffsetX = { it },
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            )
        ) + fadeIn(animationSpec = tween(500))
    ) {
        SmartShieldCard(
            modifier = Modifier.fillMaxWidth(),
            onClick = onSelect,
            elevation = if (isSelected) 8.dp else 2.dp,
            backgroundColor = if (isSelected) {
                SmartShieldColors.Primary.copy(alpha = 0.08f)
            } else {
                SmartShieldColors.Surface
            },
            borderColor = if (isSelected) {
                SmartShieldColors.Primary
            } else {
                SmartShieldColors.CardBorder
            },
            borderWidth = if (isSelected) 2.dp else 1.dp,
            cornerRadius = 16.dp
        ) {
            Box {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Premium Professional Flag Display
                    Box(
                        modifier = Modifier
                            .size(56.dp)
                            .shadow(6.dp, CircleShape)
                            .background(
                                brush = androidx.compose.ui.graphics.Brush.radialGradient(
                                    colors = listOf(
                                        SmartShieldColors.Primary.copy(alpha = 0.1f),
                                        SmartShieldColors.SurfaceVariant,
                                        SmartShieldColors.Surface
                                    )
                                ),
                                shape = CircleShape
                            )
                            .border(
                                width = 3.dp,
                                color = if (isSelected) SmartShieldColors.Primary else SmartShieldColors.Primary.copy(alpha = 0.3f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = language.flagEmoji,
                            fontSize = 28.sp,
                            modifier = Modifier.scale(1.2f)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = language.name,
                                style = MaterialTheme.typography.titleMedium,
                                color = if (isSelected) SmartShieldColors.Primary else SmartShieldColors.OnSurface
                            )

                            if (language.isRtl) {
                                Spacer(modifier = Modifier.width(8.dp))
                                StatusBadge(
                                    text = "RTL",
                                    status = StatusType.Info,
                                    size = BadgeSize.Small
                                )
                            }
                        }

                        Text(
                            text = language.nativeName,
                            style = MaterialTheme.typography.bodyMedium,
                            color = SmartShieldColors.OnSurfaceVariant,
                            fontWeight = FontWeight.Medium
                        )

                        if (language.region.isNotEmpty()) {
                            Text(
                                text = language.region,
                                style = MaterialTheme.typography.bodySmall,
                                color = SmartShieldColors.Gray500
                            )
                        }
                    }

                    // Professional Selection Indicator
                    AnimatedVisibility(
                        visible = isSelected,
                        enter = scaleIn(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessHigh
                            )
                        ) + fadeIn(),
                        exit = scaleOut() + fadeOut()
                    ) {
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .background(
                                    color = SmartShieldColors.Primary,
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Selected",
                                tint = SmartShieldColors.OnPrimary,
                                modifier = Modifier.size(18.dp)
                            )
                        }
                    }
                }

                // Professional Popular Badge
                if (showPopularBadge) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .offset(x = (-8).dp, y = 8.dp)
                    ) {
                        StatusBadge(
                            text = "Popular",
                            status = StatusType.Warning,
                            size = BadgeSize.Small
                        )
                    }
                }
            }
        }
    }
}
